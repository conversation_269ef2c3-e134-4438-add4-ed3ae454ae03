<!-- 全局loading指示器 - 放在最外层确保悬浮 -->
<div class="global-loading-banner" v-show="isAnyLoading">
    <div class="global-loading-content">
        <div class="global-spinner"></div>
        <span class="global-loading-text">{{ currentLoadingMessage }}</span>
    </div>
</div>

<!-- 主要内容区域 -->
<div class="travel-planning-container">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                    <circle cx="12" cy="10" r="3"/>
                </svg>
                智能旅游规划
            </h1>
            <p class="page-subtitle">为您量身定制完美的旅行体验</p>
        </div>
        <div class="header-decoration">
            <div class="decoration-circle"></div>
            <div class="decoration-circle"></div>
            <div class="decoration-circle"></div>
        </div>
    </div>

    <!-- 旅游规划表单 -->
    <div class="travel-form">
        <!-- 进度指示器 -->
        <div class="form-progress">
            <div class="progress-step active">
                <div class="step-number">1</div>
                <span class="step-label">基本信息</span>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step">
                <div class="step-number">2</div>
                <span class="step-label">生成规划</span>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step">
                <div class="step-number">3</div>
                <span class="step-label">完成</span>
            </div>
        </div>

        <!-- 地点选择区域 -->
        <div class="location-section">
            <h3 class="section-title">
                <svg class="section-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                    <circle cx="12" cy="10" r="3"/>
                </svg>
                选择出发地与目的地
            </h3>

            <div class="location-inputs">
                <!-- 旅游起点 -->
                <div class="form-item location-item">
                    <label for="start-tipinput" class="form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <circle cx="12" cy="12" r="3"/>
                        </svg>
                        出发地
                    </label>
                    <div class="input-wrapper">
                        <input id="start-tipinput" class="form-input" v-model="s_address" type="text"
                            placeholder="请输入出发城市/地点" :disabled="!showBtn"
                            autocomplete="off">
                        <div class="input-decoration"></div>
                    </div>
                </div>

                <!-- 路线连接线 -->
                <div class="route-connector">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M5 12h14"/>
                        <path d="m12 5 7 7-7 7"/>
                    </svg>
                </div>

                <!-- 旅游终点 -->
                <div class="form-item location-item">
                    <label for="end-tipinput" class="form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/>
                            <path d="M4 22v-7"/>
                        </svg>
                        目的地
                    </label>
                    <div class="input-wrapper">
                        <input id="end-tipinput" class="form-input" v-model="e_address" type="text"
                            placeholder="请输入目的地城市/景点" :disabled="!showBtn"
                            autocomplete="off">
                        <div class="input-decoration"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 旅行详情区域 -->
        <div class="travel-details-section">
            <h3 class="section-title">
                <svg class="section-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                    <line x1="16" y1="2" x2="16" y2="6"/>
                    <line x1="8" y1="2" x2="8" y2="6"/>
                    <line x1="3" y1="10" x2="21" y2="10"/>
                </svg>
                设置旅行参数
            </h3>

            <div class="details-grid">
                <!-- 出发日期 -->
                <div class="form-item detail-item">
                    <label for="start-dateinput" class="form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                            <line x1="16" y1="2" x2="16" y2="6"/>
                            <line x1="8" y1="2" x2="8" y2="6"/>
                            <line x1="3" y1="10" x2="21" y2="10"/>
                        </svg>
                        出发日期
                    </label>
                    <div class="input-wrapper">
                        <input type="date" class="form-input date-input" v-model="startDate" :disabled="!showBtn">
                        <div class="input-decoration"></div>
                    </div>
                </div>

                <!-- 游玩天数 -->
                <div class="form-item detail-item">
                    <label for="datesinput" class="form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                        游玩天数
                    </label>
                    <div class="input-wrapper">
                        <select id="datesinput" class="form-input select-input" v-model.number="dates" :disabled="!showBtn">
                            <option v-for="n in 5" :value="n" :key="n" :selected="n === 3">{{ n }}天</option>
                        </select>
                        <div class="input-decoration"></div>
                    </div>
                </div>

                <!-- 规划模式 -->
                <div class="form-item detail-item">
                    <label for="plan-mode" class="form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                            <line x1="12" y1="22.08" x2="12" y2="12"/>
                        </svg>
                        旅行模式
                    </label>
                    <div class="input-wrapper">
                        <select id="plan-mode" class="form-input select-input" v-model="plan_mode" :disabled="!showBtn">
                            <option value="往返">往返旅行</option>
                            <option value="单程">单程旅行</option>
                        </select>
                        <div class="input-decoration"></div>
                    </div>
                </div>

                <!-- 交通方式 -->
                <div class="form-item detail-item">
                    <label for="travel-mode" class="form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M7 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0"/>
                            <path d="M17 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0"/>
                            <path d="M5 17h-2v-6l2-5h9l4 5h1a2 2 0 0 1 2 2v4h-2"/>
                            <path d="M9 17v-6h4v6"/>
                            <path d="M2 6h15"/>
                        </svg>
                        交通方式
                    </label>
                    <div class="input-wrapper">
                        <select id="travel-mode" class="form-input select-input" v-model="travel_mode" :disabled="!showBtn">
                            <option value="自驾">自驾出行</option>
                            <option value="租车">租车出行</option>
                        </select>
                        <div class="input-decoration"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 操作按钮区域 -->
        <div class="action-section">
            <div class="button-group">
                <button v-show="showBtn" class="btn-primary btn-planning" @click="planningNew">
                    <div class="btn-content">
                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4.5 16.5c-1.5 1.5-1.5 4.5 0 6s4.5 1.5 6 0l1-1"/>
                            <path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 0 2.12l.88.88a1.5 1.5 0 0 0 2.12 0L18 10"/>
                            <path d="M9 5l8 8"/>
                            <path d="M21 3l-6 6"/>
                        </svg>
                        <span class="btn-text">开始规划</span>
                    </div>
                    <div class="btn-shine"></div>
                </button>

                <button v-show="showBtn" class="btn-secondary btn-reset" @click="resetFormData" title="清除所有输入内容" :disabled="!showBtn">
                    <div class="btn-content">
                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M3 21v-5h5"/>
                        </svg>
                        <span class="btn-text">重置表单</span>
                    </div>
                </button>
            </div>

            <!-- 加载状态 -->
            <div class="loading-state" v-show="!showBtn">
                <div class="loading-animation">
                    <div class="loading-spinner">
                        <svg viewBox="0 0 50 50">
                            <circle cx="25" cy="25" r="20" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                    <div class="loading-text">
                        <span class="loading-message">正在为您规划完美旅程...</span>
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 天数导航按钮 -->
    <div class="day-navigation" v-show="contents.length > 0">
        <button v-for="(content, index) in contents" :key="'nav-' + index" :class="['day-nav-btn', {
                'active': activeDayDetailIndex === index,
                'completed': isDayCompleted(content),
                'disabled': !isDayCompleted(content)
            }]" @click="selectDay(index)" :disabled="!isDayCompleted(content)">
            <span class="day-number">第{{ index + 1 }}天</span>
            <span class="day-title">{{ getDayTitle(content, index) }}</span>
        </button>
    </div>

    <!-- 天数详情面板 -->
    <div class="day-detail-panel" v-show="activeDayDetailIndex !== -1 && contents.length > 0 && contents[activeDayDetailIndex]">
        <div class="panel-header">
            <div class="panel-icon">✨</div>
            <h3 class="panel-title">第{{ activeDayDetailIndex + 1 }}天行程详情</h3>
            <p class="panel-subtitle">{{ activeDayDetailIndex !== -1 && contents[activeDayDetailIndex] ? getDayTitle(contents[activeDayDetailIndex], activeDayDetailIndex) : '' }}</p>
        </div>
        <div class="panel-body">
            <div class="panel-features">
                <button class="feature-button"
                        :class="{ 'available': isSectionAvailable('weather', activeDayDetailIndex), 'active': expandedSectionType === 'weather' }"
                        :disabled="!isSectionAvailable('weather', activeDayDetailIndex)"
                        @click="handleDetailPanelClick('weather')"
                        v-show="activeDayDetailIndex === 0">
                    <span class="feature-icon">🌤️</span>
                    <span class="feature-text">天气</span>
                </button>
                <button class="feature-button"
                        :class="{ 'available': isSectionAvailable('rent', activeDayDetailIndex), 'active': expandedSectionType === 'rent' }"
                        :disabled="!isSectionAvailable('rent', activeDayDetailIndex)"
                        @click="handleDetailPanelClick('rent')"
                        v-show="activeDayDetailIndex === 0 && travel_mode === '租车'">
                    <span class="feature-icon">🚗</span>
                    <span class="feature-text">租车</span>
                </button>
                <button class="feature-button"
                        :class="{ 'available': isSectionAvailable('driving', activeDayDetailIndex), 'active': expandedSectionType === 'driving' }"
                        :disabled="!isSectionAvailable('driving', activeDayDetailIndex)"
                        @click="handleDetailPanelClick('driving')">
                    <span class="feature-icon">🗺️</span>
                    <span class="feature-text">路线</span>
                </button>
                <button class="feature-button"
                        :class="{ 'available': isSectionAvailable('view', activeDayDetailIndex), 'active': expandedSectionType === 'view' }"
                        :disabled="!isSectionAvailable('view', activeDayDetailIndex)"
                        @click="handleDetailPanelClick('view')">
                    <span class="feature-icon">🏞️</span>
                    <span class="feature-text">景点</span>
                </button>
                <button class="feature-button"
                        :class="{ 'available': isSectionAvailable('food', activeDayDetailIndex), 'active': expandedSectionType === 'food' }"
                        :disabled="!isSectionAvailable('food', activeDayDetailIndex)"
                        @click="handleDetailPanelClick('food')">
                    <span class="feature-icon">🍜</span>
                    <span class="feature-text">美食</span>
                </button>
                <button class="feature-button"
                        :class="{ 'available': isSectionAvailable('hotel', activeDayDetailIndex), 'active': expandedSectionType === 'hotel' }"
                        :disabled="!isSectionAvailable('hotel', activeDayDetailIndex)"
                        @click="handleDetailPanelClick('hotel')">
                    <span class="feature-icon">🏨</span>
                    <span class="feature-text">住宿</span>
                </button>
            </div>
            <div class="panel-actions">
                <button class="collapse-all-btn" @click="collapseAll" v-show="expandedSectionType !== null">
                    <span class="btn-icon">📝</span>
                    <span class="btn-text">收起全部</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 旅游规划结果展示区，动态生成 -->
    <div id="scroll-area" class="scroll-container" ref="scrollContainer">
        <div v-for="(content, index) in contents" :key="'day-' + index">
            <!-- 天气信息 -->
            <div data-dynamic-item class="answer-area-container"
                v-show="index === 0 && content.weatherCompleted !== 0">
                <div class="section-header weather-header" @click="handleSectionHeaderClick('weather', index)">
                    <div class="header-icon">🌤️</div>
                    <div class="header-title">天气信息</div>
                    <div class="header-subtitle">Weather Information</div>
                    <div class="header-toggle" v-show="shouldShowSection('weather', index)">▼</div>
                </div>
                <div class="section-content" v-show="shouldShowSection('weather', index)">
                    <div class="think-area" v-show="content.weatherCompleted === 1">{{ content.think }}</div>
                    <div class="answer-area" v-html="parseMarkdown(content.weather)"></div>
                </div>
            </div>

            <!-- 租车信息 -->
            <div data-dynamic-item class="answer-area-container"
                v-show="index === 0 && content.rentCompleted !== 0">
                <div class="section-header rent-header" @click="handleSectionHeaderClick('rent', index)">
                    <div class="header-icon">🚗</div>
                    <div class="header-title">租车方案</div>
                    <div class="header-subtitle">Car Rental</div>
                    <div class="header-toggle" v-show="shouldShowSection('rent', index)">▼</div>
                </div>
                <div class="section-content" v-show="shouldShowSection('rent', index)">
                    <div class="think-area" v-show="content.rentCompleted === 1">{{ content.think }}</div>
                    <div class="answer-area" v-html="parseMarkdown(content.rent)"></div>
                    <div class="answer-action-input-container" v-show="content.rentCompleted === 2 && !rent_customized">
                        <input :value="rent_requirements" type="text" class="answer-action-input"
                            placeholder="请输入您的租车需求，我们可以根据您的需求重新定制一次..." @input="updateRentRequirements"
                            @keypress.enter="handleRentCustomize(index)">
                    </div>
                    <button class="answer-action-btn" v-show="content.rentCompleted === 2 && !rent_customized"
                        @click="handleActionClick('rent', index)">
                        <span class="btn-icon">🚗</span>
                        <span class="btn-text">定制</span>
                    </button>
                </div>
            </div>

            <!-- 路线规划 -->
            <div data-dynamic-item class="answer-area-container"
                v-show="content.drivingCompleted !== 0">
                <div class="section-header driving-header" @click="handleSectionHeaderClick('driving', index)">
                    <div class="header-icon">🗺️</div>
                    <div class="header-title">路线规划</div>
                    <div class="header-subtitle">Route Planning</div>
                    <div class="header-toggle" v-show="shouldShowSection('driving', index)">▼</div>
                </div>
                <div class="section-content" v-show="shouldShowSection('driving', index)">
                    <div class="think-area" v-show="content.drivingCompleted === 1">{{ content.think }}</div>
                    <div class="answer-area" v-html="parseMarkdown(content.driving)"></div>
                    <div class="answer-action-input-container" v-show="content.drivingCompleted === 2 && !plan_customized">
                        <select class="answer-action-input" v-model="plan_requirements">
                            <option v-for="option in planOptions" :key="option.value" :value="option.value">
                                {{ option.text }}
                            </option>
                        </select>
                    </div>
                    <button class="answer-action-btn" v-show="content.drivingCompleted === 2 && !plan_customized"
                        @click="handleActionClick('driving', index)">
                        <span class="btn-icon">📍</span>
                        <span class="btn-text">重新规划(一次免费机会)</span>
                    </button>
                </div>
            </div>

            <!-- 地图信息 -->
            <div class="planning-box"
                v-show="'amap' in content && shouldShowSection('driving', index)">
                <div class="section-header map-header">
                    <div class="header-icon">🗺️</div>
                    <div class="header-title">路线地图</div>
                    <div class="header-subtitle">Route Map</div>
                </div>
                <div class="map-wrapper" :class="{ 'map-maximized': maximizedMapIndex === index }">
                    <div v-if="maximizedMapIndex === index" class="map-overlay" @click="toggleMapSize(index)"></div>
                    <div class="map-controls">
                        <button class="map-control-btn" @click="toggleMapSize(index)"
                            :title="maximizedMapIndex === index ? '缩小地图 (ESC)' : '最大化地图'">
                            <span v-if="maximizedMapIndex === index">✕</span>
                            <span v-else>🗖</span>
                        </button>
                    </div>
                    <div :id="`map-container-${index}`" class="map-container"></div>
                </div>
            </div>

            <!-- 景点信息 -->
            <div data-dynamic-item class="answer-area-container"
                v-show="content.viewCompleted !== 0">
                <div class="section-header view-header" @click="handleSectionHeaderClick('view', index)">
                    <div class="header-icon">🏞️</div>
                    <div class="header-title">景点推荐</div>
                    <div class="header-subtitle">Tourist Attractions</div>
                    <div class="header-toggle" v-show="shouldShowSection('view', index)">▼</div>
                </div>
                <div class="section-content" v-show="shouldShowSection('view', index)">
                    <div class="content-wrapper">
                        <div class="think-area" v-show="content.viewCompleted === 1">{{ content.think }}</div>
                        <div class="answer-area" v-html="this.parseMarkdown('**景点推荐:**\n\n')"></div>
                        <div class="content-item" v-for="(view, viewIndex) in content.view" :key="'view-' + viewIndex">
                            <span class="icon-ori">🏞️</span>
                            {{ view.name }}
                            <img :src="view.url" alt="景点图片" class="view-image">
                            <div class="answer-area" v-html="this.parseMarkdown(view.info)"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 美食信息 -->
            <div data-dynamic-item class="answer-area-container"
                v-show="content.foodCompleted !== 0">
                <div class="section-header food-header" @click="handleSectionHeaderClick('food', index)">
                    <div class="header-icon">🍜</div>
                    <div class="header-title">美食推荐</div>
                    <div class="header-subtitle">Local Cuisine</div>
                    <div class="header-toggle" v-show="shouldShowSection('food', index)">▼</div>
                </div>
                <div class="section-content" v-show="shouldShowSection('food', index)">
                    <div class="content-wrapper">
                        <div class="think-area" v-show="content.foodCompleted === 1">{{ content.think }}</div>
                        <div class="answer-area" v-html="this.parseMarkdown('**美食推荐:**\n\n')"></div>
                        <div class="content-item" v-for="(food, foodIndex) in content.food" :key="'food-' + foodIndex">
                            <span class="icon-ori">🍜</span>
                            {{ food.name }}
                            <img :src="food.url" alt="美食图片" class="food-image">
                            <div class="answer-area" v-html="this.parseMarkdown(food.info)"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 住宿信息 -->
            <div data-dynamic-item class="answer-area-container"
                v-show="content.hotelCompleted !== 0">
                <div class="section-header hotel-header" @click="handleSectionHeaderClick('hotel', index)">
                    <div class="header-icon">🏨</div>
                    <div class="header-title">住宿推荐</div>
                    <div class="header-subtitle">Accommodation</div>
                    <div class="header-toggle" v-show="shouldShowSection('hotel', index)">▼</div>
                </div>
                <div class="section-content" v-show="shouldShowSection('hotel', index)">
                    <div class="content-wrapper">
                        <div class="think-area" v-show="content.hotelCompleted === 1">{{ content.think }}</div>
                        <div class="answer-area" v-html="this.parseMarkdown('**住宿推荐:**\n\n')"></div>
                        <div class="content-item" v-for="(hotel, hotelIndex) in content.hotel" :key="'hotel-' + hotelIndex">
                            <a :href="hotel.url" target="_blank" class="hotel-link">
                                <span class="icon-ori">🏨</span>
                                {{ `携程直达：${hotel.name}` }}
                            </a>
                            <div class="answer-area" v-html="this.parseMarkdown(hotel.info)"></div>
                        </div>
                    </div>
                    <div class="answer-action-input-container" v-show="content.hotelCompleted === 2 && !hotel_customized">
                        <input :value="hotel_requirements" type="text" class="answer-action-input"
                            placeholder="请输入您的住宿需求，我们可以根据您的需求重新定制一次..." @input="updateHotelRequirements"
                            @keypress.enter="handleActionClick('hotel', index)">
                    </div>
                    <button class="answer-action-btn" v-show="content.hotelCompleted === 2 && !hotel_customized"
                        @click="handleActionClick('hotel', index)">
                        <span class="btn-icon">🏨</span>
                        <span class="btn-text">定制</span>
                    </button>
                </div>
            </div>

            <!-- 费用信息 -->
            <div data-dynamic-item class="answer-area-container"
                v-show="content.costCompleted !== 0 && (selectedDayIndex === -1 || selectedDayIndex === index)">
                <div class="section-header cost-header">
                    <div class="header-icon">💰</div>
                    <div class="header-title">费用预算</div>
                    <div class="header-subtitle">Budget Planning</div>
                </div>
                <div class="think-area" v-show="content.costCompleted === 1">{{ content.think }}</div>
                <div class="answer-area" v-html="parseMarkdown(content.cost)"></div>
            </div>
            <hr class="vitepress-divider" v-show="'cost' in content">
        </div>
    </div>
</div>

<!-- 完成提示弹窗 -->
<div v-if="showCompletionModal" class="completion-modal-overlay" @click="handleCompletionModalConfirm">
    <div class="completion-modal" @click.stop>
        <div class="modal-header">
            <div class="modal-icon">✨</div>
            <h3 class="modal-title">规划完成！</h3>
        </div>
        <div class="modal-body">
            <p class="modal-message">您的 <strong>{{ dates }}天{{ plan_mode }}旅行规划</strong> 已经生成完毕！</p>
            <p class="modal-subtitle">从 <span class="location">{{ s_address }}</span> 到 <span class="location">{{ e_address }}</span> 的完美行程等您探索</p>
            <div class="modal-features">
                <div class="feature-item">
                    <span class="feature-icon">📍</span>
                    <span>详细路线规划</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🏞️</span>
                    <span>精选景点推荐</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🏨</span>
                    <span>优质住宿建议</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🍜</span>
                    <span>地道美食指南</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="modal-confirm-btn" @click="handleCompletionModalConfirm">
                <span class="btn-icon">👍</span>
            </button>
        </div>
    </div>
</div>

<!-- 规划失败提示弹窗 -->
<div v-if="showFailureModal" class="failure-modal-overlay" @click="handleFailureModalConfirm">
    <div class="failure-modal" @click.stop>
        <div class="modal-header failure-header">
            <div class="modal-icon">❌</div>
            <h3 class="modal-title">规划失败</h3>
        </div>
        <div class="modal-body">
            <p class="modal-message">很抱歉，您的 <strong>{{ dates }}天{{ plan_mode }}旅行规划</strong> 生成失败</p>
            <p class="modal-subtitle">{{ failureReason }}</p>
            <div class="failure-suggestions">
                <div class="suggestion-item">
                    <span class="suggestion-icon">🔄</span>
                    <span>检查网络连接后重新尝试</span>
                </div>
                <div class="suggestion-item">
                    <span class="suggestion-icon">📝</span>
                    <span>确认起终点地址填写正确</span>
                </div>
                <div class="suggestion-item">
                    <span class="suggestion-icon">⏰</span>
                    <span>稍后再试，避开网络高峰期</span>
                </div>
                <div class="suggestion-item">
                    <span class="suggestion-icon">💬</span>
                    <span>如持续失败请联系客服</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="modal-failure-btn" @click="handleFailureModalConfirm">
                <span class="btn-icon">😔</span>
                <span class="btn-text">我知道了</span>
            </button>
        </div>
    </div>
</div>

<!-- 悬浮滚动按钮 -->
<div class="floating-scroll-buttons" :class="{ show: showFloatingButtons }">
    <button class="scroll-btn scroll-to-top" @click="scrollToTop" title="滚动到顶部">
        <span class="scroll-btn-icon">⬆️</span>
    </button>
    <button class="scroll-btn scroll-to-bottom" @click="scrollToBottom" title="滚动到底部">
        <span class="scroll-btn-icon">⬇️</span>
    </button>
</div>